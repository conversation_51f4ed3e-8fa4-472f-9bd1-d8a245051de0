<template>
  <div>
    <NavigationBar
      title="菜单管理"
      left-arrow
      @click-left="onBack"
    />

    <div class="menu-content">
      <!-- B端菜单编辑 -->
      <div>
        <!-- 招牌菜分组 - 固定在顶部，不可拖拽 -->
        <div v-if="specialtyGroup">
          <MenuGroup
            :group="specialtyGroup"
            :index="0"
            :total-groups="menuGroups.length"
            :specialty-dish-count="specialtyDishCount"
            @update-group="onUpdateGroup"
            @delete-group="onDeleteGroup"
            @add-dish="onAddDish"
            @edit-dish="onEditDish"
            @delete-dish="onDeleteDish"
            @sort-dishes="onSortDishes"
            @rename-group="onRenameGroup"
            @cross-group-drag="onCrossGroupDrag"
          />
        </div>

        <!-- 普通分组 - 可拖拽排序 -->
        <div>
          <draggable
            v-model="regularGroups"
            group="groups"
            item-key="groupId"
            handle=".group-drag-handle"
            @change="onRegularGroupsChange"
          >
            <template #item="{ element: group, index }">
              <div class="group-item">
                <!-- 普通分组的拖拽按钮 -->
                <OnixIcon
                  icon="menu_m"
                  size="24"
                  class="group-drag-handle"
                />
                <MenuGroup
                  :group="group"
                  :index="index + 1"
                  :total-groups="menuGroups.length"
                  :specialty-dish-count="specialtyDishCount"
                  @update-group="onUpdateGroup"
                  @delete-group="onDeleteGroup"
                  @add-dish="onAddDish"
                  @edit-dish="onEditDish"
                  @delete-dish="onDeleteDish"
                  @sort-dishes="onSortDishes"
                  @rename-group="onRenameGroup"
                  @cross-group-drag="onCrossGroupDrag"
                />
              </div>
            </template>
          </draggable>
        </div>

        <!-- 添加分组按钮 -->
        <div class="add-group-btn" @click="onAddGroup">
          <span>增加分组</span>
        </div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <footer v-if="menuGroups.length > 0" class="bottom-actions">
      <div class="preview-btn" @click="onPreview">
        <OnixIcon icon="eyeView" size="20" />
        <div class="preview-btn-text">预览</div>
      </div>
      <Button
        type="primary"
        block
        round
        :loading="saving"
        @click="onSave"
      >
        保存
      </Button>
    </footer>

    <!-- 重命名弹窗 -->
    <RenameAlert
      ref="renameAlertRef"
      :on-audit="auditGroupName"
      @confirm="onRenameConfirm"
      @cancel="onRenameCancel"
    />
    <AlertMethod ref="alertMethodRef" />
  </div>
</template>

<script lang="ts" setup>
  import {
    ref, computed, onMounted
  } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import {
    NavigationBar,
    Button,
    showToast,
    ToastType,
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import draggable from 'vuedraggable'
  import MenuGroup from './MenuGroup.vue'
  import RenameAlert from './RenameAlert.vue'
  import AlertMethod from './AlertMethod.vue'
  import { saveMenuData, validateGroupName, mockMenuData } from '~/services/menuManage'
  import {
    IData, IMenuGroupListExtended, IDishList
  } from '~/types/menu'
  // import { postQueryMenu } from '~/services/edith_post_query_menu'

  import '~/assets/svg/menu_m.svg'
  import '~/assets/svg/eyeView.svg'

  interface Props {
    poiId?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    poiId: ''
  })

  const router = useRouter()
  const store = useStore()
  const saving = ref(false)
  const menuGroups = ref<IMenuGroupListExtended[]>([])
  const renameAlertRef = ref()
  const currentRenameGroupId = ref('')
  const alertMethodRef = ref()

  // 招牌菜分组
  const specialtyGroup = computed(() => menuGroups.value.find(g => g.isSpecialty) || null)

  // 普通分组
  const regularGroups = computed({
    get: () => menuGroups.value.filter(g => !g.isSpecialty),
    set: (value: IMenuGroupListExtended[]) => {
      // 重新组合分组：招牌菜分组 + 普通分组
      const specialty = menuGroups.value.find(g => g.isSpecialty)
      menuGroups.value = specialty ? [specialty, ...value] : value
    }
  })

  // 招牌菜数量
  const specialtyDishCount = computed(() => {
    const specialtyGroup = menuGroups.value.find(g => g.isSpecialty)
    return specialtyGroup?.dishList.length || 0
  })

  // 获取菜单数据
  const fetchMenuData = async () => {
    try {
      // 真实接口调用（暂时注释）
      // const data = await postQueryMenu({ poiId: props.poiId })

      // 使用mock数据进行测试
      const data = mockMenuData
      // 收集所有招牌菜
      const specialtyDishes: IDishList[] = []
      // 遍历所有菜品组，提取招牌菜
      data.menuGroupList.forEach(group => {
        group.dishList.forEach(dish => {
          if (dish.specialty === 1) {
            specialtyDishes.push(dish)
          }
        })
      })
      // 根据 recommendSortOrder 排序招牌菜
      specialtyDishes.sort((a, b) => {
        const orderA = a.recommendSortOrder || 0
        const orderB = b.recommendSortOrder || 0
        return orderA - orderB
      })
      // 创建招牌菜分组
      const specialtyGroup: IMenuGroupListExtended = {
        groupId: 'specialty',
        groupName: '招牌菜',
        dishList: specialtyDishes,
        sortOrder: 0,
        isSpecialty: true
      }
      // 转换原始数据，添加 isSpecialty 标识（原分组保留所有菜品）
      const regularGroups = data.menuGroupList.map(group => ({
        ...group,
        isSpecialty: false
      }))
      // 组合菜单数据：招牌菜分组 + 原始分组
      menuGroups.value = [specialtyGroup, ...regularGroups]
    } catch (error) {
      console.error('获取菜单数据失败:', error)
      showToast('获取菜单数据失败')
    }
  }

  // 进入预览模式 - 跳转到预览页面
  const onPreview = () => {
    // 这里可以跳转到预览页面或者弹出预览弹窗
    showToast('预览功能开发中')
  }

  // 返回处理
  const onBack = () => {
    router.back()
  }

  // 普通分组变更
  const onRegularGroupsChange = () => {
    // 更新sortOrder
    regularGroups.value.forEach((group, index) => {
      group.sortOrder = index + 1 // 从1开始，因为招牌菜分组是0
    })
  }

  // 重命名分组
  const onRenameGroup = (groupId: string) => {
    const group = menuGroups.value.find(g => g.groupId === groupId)
    if (!group || group.isSpecialty) return

    currentRenameGroupId.value = groupId
    // 获取所有分组名称，过滤掉当前分组
    const allGroupNames = menuGroups.value
      .filter(g => g.groupId !== groupId)
      .map(g => g.groupName)

    renameAlertRef.value?.open(group.groupName, allGroupNames)
  }

  // 审核分组名称
  const auditGroupName = async (name: string) => {
    try {
      return await validateGroupName(name)
    } catch (error) {
      console.error('审核失败:', error)
      return false
    }
  }

  // 重命名确认
  const onRenameConfirm = (newName: string) => {
    if (currentRenameGroupId.value) {
      onUpdateGroup(currentRenameGroupId.value, { groupName: newName })
      currentRenameGroupId.value = ''
    }
  }

  // 重命名取消
  const onRenameCancel = () => {
    currentRenameGroupId.value = ''
  }

  // 更新分组
  const onUpdateGroup = (groupId: string, updates: Partial<IMenuGroupListExtended>) => {
    const group = menuGroups.value.find(g => g.groupId === groupId)
    if (!group) return

    Object.assign(group, updates)
  }

  // 删除分组
  const onDeleteGroup = (groupId: string) => {
    // 只有1个分组时不能删除
    const nonSpecialtyGroups = menuGroups.value.filter(g => !g.isSpecialty)
    if (nonSpecialtyGroups.length <= 1) return

    alertMethodRef.value.showAlert({
      title: '确认删除分组？',
      message: '组内菜品也会一并删除，且无法恢复，请仔细确认后再操作',
      confirmText: '确认删除',
      cancelText: '取消',
      footerLayout: 'horizontal',
      onConfirm: () => {
        const index = menuGroups.value.findIndex(g => g.groupId === groupId)
        if (index > -1) {
          menuGroups.value.splice(index, 1)
          showToast('分组已删除')
        }
      },
      onCancel: () => {}
    })
  }

  // 添加菜品
  const onAddDish = (groupId: string) => {
    router.push({
      name: 'menuManageCreateItem',
      query: {
        groupId,
        poiId: props.poiId
      }
    })
  }

  // 编辑菜品
  const onEditDish = (dish: IDishList) => {
    // 将菜品数据存入 store
    store.dispatch('menuManage/setDish', dish)

    router.push({
      name: 'menuManageCreateItem',
      query: {
        id: dish.dishId,
        poiId: props.poiId,
        isEdit: 'true'
      }
    })
  }

  // 删除菜品
  const onDeleteDish = (groupId: string, dishId: string) => {
    alertMethodRef.value.showAlert({
      title: '确认删除菜品？',
      message: '菜品删除无法恢复，请仔细确认后再操作',
      confirmText: '确认删除',
      cancelText: '取消',
      footerLayout: 'horizontal',
      onConfirm: () => {
        const group = menuGroups.value.find(g => g.groupId === groupId)
        if (group) {
          const dishIndex = group.dishList.findIndex(d => d.dishId === dishId)
          if (dishIndex > -1) {
            group.dishList.splice(dishIndex, 1)
            showToast({
              type: ToastType.ToastBuiltInType.TEXT,
              message: '菜品已删除'
            })
          }
        }
      },
      onCancel: () => {}
    })
  }

  // 菜品排序
  const onSortDishes = (groupId: string, dishes: IDishList[]) => {
    const group = menuGroups.value.find(g => g.groupId === groupId)
    if (group) {
      group.dishList = dishes
    }
  }

  // 处理跨组拖拽
  const onCrossGroupDrag = (dragInfo: {
    fromGroupId: string
    toGroupId: string
    dishId: string
    oldIndex: number
    newIndex: number
  }) => {
    const {
      fromGroupId, toGroupId, dishId, newIndex
    } = dragInfo

    // 找到源分组和目标分组
    const fromGroup = menuGroups.value.find(g => g.groupId === fromGroupId)
    const toGroup = menuGroups.value.find(g => g.groupId === toGroupId)

    if (!fromGroup || !toGroup) return

    // 不允许拖拽到招牌菜分组
    if (toGroup.isSpecialty) {
      showToast('不能将菜品拖拽到招牌菜分组')
      return
    }

    // 找到要移动的菜品
    const dishIndex = fromGroup.dishList.findIndex(d => d.dishId === dishId)
    if (dishIndex === -1) return

    const dish = fromGroup.dishList[dishIndex]

    // 如果是从招牌菜分组拖出，需要取消specialty标识
    if (fromGroup.isSpecialty) {
      dish.specialty = 0
      dish.recommendSortOrder = undefined
    }

    // 从源分组移除菜品
    fromGroup.dishList.splice(dishIndex, 1)

    // 添加到目标分组
    toGroup.dishList.splice(newIndex, 0, dish)

    // 更新招牌菜分组（如果从招牌菜分组拖出，需要重新收集招牌菜）
    if (fromGroup.isSpecialty) {
      const specialtyDishes: IDishList[] = []
      menuGroups.value.forEach(group => {
        if (!group.isSpecialty) {
          group.dishList.forEach(dish => {
            if (dish.specialty === 1) {
              specialtyDishes.push(dish)
            }
          })
        }
      })

      // 按 recommendSortOrder 排序
      specialtyDishes.sort((a, b) => {
        const orderA = a.recommendSortOrder || 0
        const orderB = b.recommendSortOrder || 0
        return orderA - orderB
      })

      fromGroup.dishList = specialtyDishes
    }

    showToast('菜品已移动')
  }

  // 添加分组
  const onAddGroup = () => {
    // 计算普通分组的最大sortOrder
    const regularGroupsValue = menuGroups.value.filter(g => !g.isSpecialty)
    const maxOrder = Math.max(...regularGroupsValue.map(g => g.sortOrder), 0)

    const newGroup: IMenuGroupListExtended = {
      groupId: Date.now().toString(),
      groupName: '菜品分组名称',
      dishList: [],
      sortOrder: maxOrder + 1
    }

    // 添加到普通分组末尾
    menuGroups.value.push(newGroup)
  }

  // 保存菜单
  const onSave = async () => {
    saving.value = true
    try {
      const menuData: IData = {
        menuGroupList: menuGroups.value.map(group => ({
          groupId: group.groupId,
          groupName: group.groupName,
          dishList: group.dishList,
          sortOrder: group.sortOrder,
          groupStatus: group.groupStatus
        }))
      }

      await saveMenuData(props.poiId, menuData)
      showToast('菜单已保存')
    } catch (error) {
      console.error('保存失败:', error)
      showToast('保存失败，请重试')
    } finally {
      saving.value = false
    }
  }

  onMounted(() => {
    fetchMenuData()
  })
</script>

<style lang="stylus" scoped>
.nav-actions
  .action-btn
    font-size 14px
    color #1890ff
    cursor pointer

.menu-content
  padding-bottom 140px

.bottom-actions
  position fixed
  bottom 0
  left 0
  right 0
  padding 16px
  background white
  border-top 1px solid #f0f0f0
  z-index 100
  display flex
  justify-content center
  align-items center
  gap 16px

.preview-btn
  display flex
  flex-direction column
  align-items center
  justify-content center
  color rgba(0, 0, 0, 0.62)
  .preview-btn-text
    color rgba(0, 0, 0, 0.62)
    font-size 10px
    line-height 14px

.add-group-btn
  display flex
  align-items center
  justify-content center
  height 44px
  background rgba(255, 255, 255, 1)
  border-radius 12px
  color rgba(0, 0, 0, 0.62)
  font-size 16px
  font-weight 500
  line-height 24px

.group-item
  display flex
  align-items flex-start
  gap 8px
  .group-drag-handle
    color rgba(0, 0, 0, 0.45)
    margin-top 10px
</style>
