<template>
  <div class="dish-item-edit">
    <!-- 拖拽按钮 - 与菜品卡片居中对齐 -->
    <div class="dish-drag-handle">
      <OnixIcon icon="menu_m" size="24" />
    </div>

    <div class="dish-content" @click="onEdit">
      <Image
        v-if="dish.dishResourceList.length > 0"
        :src="dish.dishResourceList[0].resourceInfo"
        :alt="dish.dishName"
        :width="60"
        :height="60"
        :border-radius="6"
        fit="cover"
      />
      <div v-else class="dish-placeholder">
        <span>暂无图片</span>
      </div>

      <div class="dish-info">
        <div class="dish-name">{{ dish.dishName }}</div>
        <div class="dish-price">
          ¥{{ dish.priceItem.dishPrice }}{{ dish.priceItem.priceType === 2 ? '起' : '' }}
        </div>
      </div>
    </div>

    <div class="dish-actions">
      <span class="action-btn" @click.stop="onEdit">编辑</span>
      <Divider direction="vertical" :size="14"></Divider>
      <span class="action-btn" @click.stop="onDelete">删除</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Image, Divider } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import { IDishList } from '~/types/menu'
  import '~/assets/svg/menu_m.svg'

  interface Props {
    dish: IDishList
    inSpecialtyGroup?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    inSpecialtyGroup: false
  })

  const emit = defineEmits(['edit', 'delete'])

  const onEdit = () => {
    emit('edit', props.dish)
  }

  const onDelete = () => {
    emit('delete', props.dish)
  }
</script>

<style lang="stylus" scoped>
.dish-item-edit
  display flex
  align-items center
  height 60px
  padding 6px 0
  gap 12px
  box-sizing content-box

.dish-drag-handle
  display flex
  align-items center
  color rgba(0, 0, 0, 0.45)
  cursor grab
  transition color 0.2s ease
  padding 8px 4px
  border-radius 4px
  touch-action none
  user-select none

  &:hover
    color rgba(0, 0, 0, 0.65)
    background rgba(0, 0, 0, 0.04)

  &:active
    cursor grabbing
    transform scale(0.95)
    color rgba(0, 0, 0, 0.8)

.dish-content
  display flex
  align-items center
  flex 1
  cursor pointer
  height 100%

  .dish-image
    width 60px
    border-radius 6px
    object-fit cover
    margin-right 12px

  .dish-placeholder
    width 60px
    height 60px
    border-radius 6px
    background-color #f5f5f5
    border 1px dashed #ddd
    display flex
    align-items center
    justify-content center
    margin-right 12px

    span
      font-size 12px
      color #999

  .dish-info
    flex 1
    margin-left 12px
    display flex
    height 100%
    flex-direction column
    justify-content space-between

    .dish-name
      overflow: hidden;
      color: rgba(0, 0, 0, 0.8);
      text-overflow: ellipsis;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;

    .dish-price
      color: rgba(0, 0, 0, 0.8);
      font-family: "RED Number";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;

.dish-actions
  display flex
  height 100%
  align-items center

  .action-btn
    font-size 14px
    color rgba(0, 0, 0, 0.62)
</style>
