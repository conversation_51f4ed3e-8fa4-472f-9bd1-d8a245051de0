<template>
  <div class="image-update-body">
    <div class="container">
      <div
        v-for="(image, index) in imageList"
        :key="index"
        class="image-item-wrapper"
      >
        <img
          class="img"
          :src="image"
          @click="() => handleClickImage(index)"
        >
        <div class="img-mask" />

        <OnixIcon v-show="!prohibitOperation && !isPreview" icon="bannerCloseC" class="delete-icon" @click.stop="() => handleDelete(index)"> </OnixIcon>

      </div>
      <ConfigProvider :color-mode="colorMode">
        <Uploader
          v-if="imageList.length < maxCount && !isPreview"
          multiple
          accept="image/jpg,image/png,image/jpeg"
          :max-count="maxCount"
          :description="finalDescription"
          @success="handleSuccess"
        >
          <div class="uploader-item">
            <OnixIcon icon="addB" class="onix-icon-add-b"></OnixIcon>
            <div class="uploader-num">{{ imageList.length }}/{{ maxCount }}</div>
          </div>
        </Uploader>
      </ConfigProvider>
    </div>
    <ImagePreview
      :header-style="headerStyle"
      :visible="visible"
      :data="imageList"
      :current-index="selectedIndex"
      @endReached="handleEndReached"
      @close="handleClose"
    />
  </div>
</template>

<script setup lang="ts">
  // @xhs
  import { Uploader, ConfigProvider } from '@xhs/reds-h5-next'
  import ImagePreview from '@xhs/imagepreviewh5'
  import { computed } from 'vue'
  import OnixIcon from '@xhs/onix-icon'

  // 数据
  import { useStore } from 'vuex'
  import useCompose from './useCompose'

  // 静态资源
  import '~/assets/svg/addB.svg'
  import '~/assets/svg/bannerCloseC.svg'

  const props = defineProps<{
    modelValue: any[]
    maxCount?: number
    description?: string
    isPreview: boolean
    prohibitOperation: boolean // 营业执照禁止操作
    maxSize?: number
  }>()

  // 定义 emit
  const emit = defineEmits(['update:modelValue'])

  // 初始化vuex
  const store = useStore()
  const {
    colorMode,
  } = store.state.ShopPreviewStore

  // 使用 hook，可以传入配置项
  const {
    maxCount,
    imageList,
    description,
    visible,
    selectedIndex,
    handleSuccess,
    handleClickImage,
    handleClose,
    handleDelete,
    handleEndReached,
  } = useCompose({
    maxCount: props.maxCount || 9,
    modelValue: props.modelValue,
    maxSize: props.maxSize
  }, emit, props)

  // 优先使用props中传入的description
  const finalDescription = computed(() => props.description || description.value)

  // 样式
  const headerStyle = {
    marginTop: '30px',
  }

</script>
<style lang="stylus" scoped>
@import '@xhs/water-kaipingyidongBduan/index.css'; // 主题商店样式

.image-update-body
  width 100%

  .container
    display flex
    flex-wrap: wrap
    gap 8px
    width 100%

    .image-item-wrapper
      position: relative
      width: 71px
      height: 71px

      img
        width: 100%
        height: 100%
        border-radius: 6px
        object-fit: cover

      // 新增遮罩层样式
      .img-mask
        position: absolute
        top: 0
        left: 0
        width: 100%
        height: 100%
        background-color: rgba(0, 0, 0, 0.04)
        border-radius: 6px
        pointer-events: none

      .delete-icon
        width 16px
        height 16px
        z-index: 1
        position: absolute
        top: -5px
        right: -6px

    .uploader-item
      width 71px
      height 71px
      display flex
      flex-direction: column
      justify-content center
      align-items center
      box-sizing border-box
      background: rgba(48, 48, 52, 0.05)
      border-radius 6px

      .onix-icon-add-b
        width 24px
        height 24px

      .uploader-num
        margin-top: 4px
        color rgba(0, 0, 0, 0.45)
        font-size 10px
        font-weight 400
        line-height 12px

</style>
