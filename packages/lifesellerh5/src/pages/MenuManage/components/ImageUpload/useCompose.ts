// vue
import {
  ref, computed, toRef, Ref, ComputedRef, watch,
} from 'vue'

// 依赖
import { showToast, ToastType } from '@xhs/reds-h5-next'

// 方法
import { uploadImage } from '../../utils/upload/imgUpload'

interface UseComposeOptions {
  maxCount?: number
  modelValue?: any[] // 接收父组件传入的完整对象数组
  maxSize?: number // 图片限制大小
}

interface UploadedImage {
  url?: string
  previewUrl?: string
  [key: string]: any
}

// 定义返回类型接口
interface UseComposeReturn {
  maxCount: Ref<number>
  images: Ref<UploadedImage[]>
  imageList: ComputedRef<(string | undefined)[]>
  description: ComputedRef<string>
  visible: Ref<boolean>
  selectedIndex: Ref<number>
  handleSuccess: (file: any) => void
  handleClickImage: (index: number) => void
  handleClose: () => void
  handleDelete: (index: number) => void
  handleEndReached: () => void
}

export default function useCompose(options: UseComposeOptions = {}, emitter: Function, props: any): UseComposeReturn {
  // 初始化
  const maxCount = ref(options.maxCount || 9)

  // 使用 toRef 创建对 props.modelValue 的响应式引用
  const modelValueRef = toRef(props, 'modelValue')

  // 初始化images，使用父组件传入的modelValue
  const initialImages = Array.isArray(options.modelValue)
    ? options.modelValue.map(item => {
      // 如果父组件传入的是字符串数组，转成对象
      if (typeof item === 'string') {
        return { url: item }
      }
      // 如果已经是对象，则直接使用
      return item
    })
    : []
  const images = ref<UploadedImage[]>(initialImages)

  // 提取图片列表里的url供其展示
  const imageList = computed(() => images.value.map(img => img.url || img.previewUrl))

  // 监听图片变化，同步到父组件 - 注意这里发送完整的images对象
  watch(images, newImages => {
    emitter('update:modelValue', newImages)
  }, { deep: true })

  // 当父组件的值变化时，更新本地数据
  watch(() => modelValueRef, newValue => {
    if (newValue.value && JSON.stringify(newValue.value) !== JSON.stringify(images.value)) {
      const newImages = newValue.value.map(item => {
        if (typeof item === 'string') {
          return { url: item }
        }
        return item
      })
      images.value = newImages
    }
  }, { deep: true })

  // 当前上传图片个数
  const description = computed(() => `${images.value.length}/${maxCount.value}`)

  // 预览图片相关状态
  const visible = ref(false)
  const selectedIndex = ref(0)

  // 上传成功
  const handleSuccess = (file: any) => {
    const promises = file.map((item: any) => {
      if (item.file) {
        // 限制上传大小
        const upload = uploadImage
        return upload(item.file, options.maxSize || 0).then((res: any) => res)
      }
      return null
    }).filter(Boolean)

    Promise.all(promises).then((results: any[]) => {
      if (Array.isArray(results) && results.length > 0 && results[0]) {
        // 确保不超过最大数量
        const availableSlots = maxCount.value - images.value.length
        const newImages = results.slice(0, availableSlots)
        if (newImages.length > 0) {
          images.value = [...images.value, ...(newImages as UploadedImage[])]
          console.log('images.value', images.value)
        }
      }
    })
      .catch(error => {
        showToast({
          type: ToastType.ToastBuiltInType.TEXT,
          message: error || '上传失败请, 稍后再试',
        })
      })
  }

  // 预览图片方法
  const handleClickImage = (index: number) => {
    selectedIndex.value = index
    visible.value = true
  }

  // 关闭预览
  const handleClose = () => {
    visible.value = false
    selectedIndex.value = 0
  }

  // 删除图片
  const handleDelete = (index: number) => {
    const newImages = [...images.value]
    newImages.splice(index, 1)
    images.value = newImages
  }

  // 处理滚动到底部加载更多
  const handleEndReached = () => {
    images.value = [...images.value, ...images.value]
  }

  return {
    maxCount,
    images,
    imageList,
    description,
    visible,
    selectedIndex,
    handleSuccess,
    handleClickImage,
    handleClose,
    handleDelete,
    handleEndReached,
  }
}
