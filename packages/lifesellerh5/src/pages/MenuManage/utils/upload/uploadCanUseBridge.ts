import { getAppInfo } from '@xhs/ozone-bridge'
import { commonChecks } from '@xhs/ozone-detector'
import { isVersionLessThan } from '~/utils/isVersionLessThan'

// 获取App版本号
const getAppVersion = async () => {
  const res = await getAppInfo()
  return res?.version || ''
}
let versionPromise:Promise<string>
const debounceGetAppVersion = async () => {
  if (versionPromise) return versionPromise
  versionPromise = getAppVersion()
  return versionPromise
}

// 新容器或者指定版本的小红书App使用bridge上传
export default async function uploadCanUseBridge() {
  try {
    const appVersion = await debounceGetAppVersion()
    return !!(commonChecks.isXHS && isVersionLessThan('7.17', appVersion))
  } catch (err) {
    console.error(err)
    return false
  }
}
