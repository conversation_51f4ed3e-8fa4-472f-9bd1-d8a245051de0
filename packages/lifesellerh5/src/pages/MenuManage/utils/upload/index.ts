import { UploadOption } from './types'
import uploadCanUseBridge from './uploadCanUseBridge'
import uploadByBridge from './uploadByBridge'

const defaults:UploadOption = {
  type: 'image',
  maxSize: 2 * 1024 * 1024,
  maxCount: 10,
  mode: 'selectMedia', // 图片 ｜ 相机 bridge 选项,
  // cropRatio: 1, // 剪切比例 bridge 选项
  needCrop: false, // 是否需要剪切
  // onProgress?: (item: UploadItemRet) => void
}

// 通用上传方法
async function upload(option:UploadOption) {
  const finalOptions = { ...defaults, ...option }

  const canUseBridge = await uploadCanUseBridge()

  // 新容器或者指定版本的小红书App使用bridge上传
  if (canUseBridge) {
    return uploadByBridge(finalOptions)
  }
}

export default upload
