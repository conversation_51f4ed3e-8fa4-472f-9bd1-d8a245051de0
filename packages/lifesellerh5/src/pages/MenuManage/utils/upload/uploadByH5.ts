import Uploader from '@xhs/uploader'
import { getUploaderToken } from '~/services/uploadToken'

export const getFiles = (limit = 1, p: Record<string, string | undefined>): Promise<FileList> => {
  const props = p || {}

  const multiple = limit > 1
  const id = 'ultra-upload-input'
  // eslint-disable-next-line no-underscore-dangle
  let _input = document.getElementById(id) as HTMLInputElement
  if (!_input) {
    _input = document.createElement('input')
    _input.id = id
    _input.type = 'file'
    document.body.appendChild(_input)
  }
  Object.keys(props).forEach(key => {
    const value = props[key]
    if (value === undefined) {
      _input.removeAttribute(key)
    } else {
      _input.setAttribute(key, value)
    }
  })
  _input.accept = props.accept || 'image/*'
  _input.style.display = 'none'
  _input.value = ''
  _input.multiple = multiple

  _input.click()

  return new Promise((resolve, reject) => {
    _input.onchange = () => {
      const files = (_input as HTMLInputElement).files
      if (!files || !files.length) {
        reject(new Error('未选择文件'))
      } else {
        resolve(files)
      }
    }
  })
}

type UploadType = 'takePicture' | 'selectMedia'
/**
 * 选择图片上传
 */
export const getFilesByType = async (type: UploadType, limit: number = 1, p?: Record<string, string | undefined>) => {
  const props = p || {}
  props.capture = type === 'takePicture' ? 'camera' : undefined
  props.accept = type === 'takePicture' ? 'image/*' : undefined
  const files = await getFiles(limit, props)
  return files
}

/**
 * 上传文件
 * @param file File
 */
export const uploadFile = async (file: File) => {
  const res = await new Uploader({
    bizName: 'fls',
    scene: 'nosid',
    getToken: getUploaderToken,
  }).post({
    Body: file,
    SliceSize: 1024 * 1024 * 10,
  })
  const name = file?.name || '未知文件'

  const {
    // @ts-ignore
    url, previewUrl, bizName, scene, fileId, cloudType,
  } = res?.data || {}
  const result = {
    name,
    // 提交时将url替换成永久url
    url: previewUrl || url,
    fileType: file.type,
    fileName: file.name,
    // 宽高暂时拿不到
    width: 0,
    height: 0,
    uploaderInfoModel: {
      url: previewUrl || url,
      bizName,
      scene,
      fileId,
      cloudType,
      isSecret: true,
    },
  }
  return result
}
