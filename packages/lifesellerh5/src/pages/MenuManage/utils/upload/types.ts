export interface UploadItemRet {
  id: string
  height: number
  width: number
  extension: string
  uploadPercent: number
  path: string
  link: string
  size?: number
  error?: string
  file?: File

  scene?:string
  fileId?:number
  bizName?:string
}

export interface UploadOption {
  type?: 'image' | 'video'
  maxSize?: number
  maxCount?: number
  mode?: 'selectMedia' | 'tackPicture' // 图片 ｜ 相机 bridge 选项,
  cropRatio?: number // 剪切比例 bridge 选项
  needCrop?: boolean // 是否需要剪切
  onProgress?: (item: UploadItemRet) => void
}

export interface DataItem {
  id?: string
  link?: string // ark后台上传是没有link的
  path?: string // 上传key一定有
  image?: string // base64 || 临时文件
  height?: number
  width?: number
  extension?: string
  uploadPercent?: number
  size?:number
  error?: string
}

export type DataList = DataItem[]
