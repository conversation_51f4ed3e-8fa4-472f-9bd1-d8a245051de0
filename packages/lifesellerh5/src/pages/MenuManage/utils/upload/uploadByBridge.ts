// @ts-ignore
import uniqueId from 'lodash.uniqueid'
// @ts-ignore
import { selectMedia, takePicture } from '@xhs/ozone-bridge'
import upload from '@xhs/rex-uploader'
import { Toast } from '@xhs/riant'

import { UploadItemRet, UploadOption } from './types'

interface ImageItem {
  width: number
  height: number
  size: number
  path: any
}

async function getFiles(
  option: UploadOption,
):Promise<{
    result: 0 | 1
    value: ImageItem[]
  }> {
  const {
    type = 'image',
    maxCount = 1,
    mode = 'selectMedia',
    needCrop = false,
  } = option
  const typeMap = {
    image: 0,
    video: 1,
  }
  const t = typeMap[type] as 0 | 1
  if (mode === 'selectMedia') {
    // @ts-ignore
    return selectMedia({
      type: t,
      maxCount,
      image: {
        needCrop,
        cropRatioList: ['11'],
      },
      // @ts-ignore
      needCrop,
      cropRatioList: ['11'],
      theme: { submitBtnText: '下一步' },
    }).catch((err: any) => {
      throw err
    })
  }
  // @ts-ignore
  return takePicture({
    needCrop,
    // @ts-ignore
    type: typeMap[type],
    cropRatioList: ['11'],
  })
}

function uploadItem(item: ImageItem, retItem: UploadItemRet, option: UploadOption) {
  const {
    onProgress,
  } = option
  const { path } = item
  return new Promise((resolve, reject) => {
    const onError = (errMsg:string = '上传图片失败，请重试') => {
      retItem.uploadPercent = 100
      retItem.error = errMsg
      onProgress?.(retItem)
      Toast(errMsg)
      reject(errMsg)
    }

    upload({
      business: 10,
      files: [{ path }],
      fileType: 'image',
      onComplete: (result: any[]) => {
        if (result[0]?.cloudCode === 4) {
          retItem.scene = result[0]?.scene
          retItem.fileId = result[0]?.fileId
          retItem.link = result[0]?.previewUrl
          retItem.bizName = 'sale'
          onProgress?.(retItem)
          resolve(retItem)
        } else if (result[0]?.cdnlink) {
          retItem.link = `https://qimg.xiaohongshu.com/${result[0]?.cdnlink}`
          retItem.path = `${result[0]?.cdnlink}`
          retItem.uploadPercent = 100
          onProgress?.(retItem)
          resolve(retItem)
        } else {
          onError()
        }
      },
      onError: (err:any) => {
        onError(err.message)
      },
      onProgress: (n:number) => {
        retItem.uploadPercent = Math.round(n * 100)
        onProgress?.(retItem)
      },
    })
  })
}

export default async function uploadByBridge(option: UploadOption) {
  const {
    maxSize = 5 * 1024 * 1024, onProgress,
    maxCount = 1,
    cropRatio,
    mode,
  } = option

  // 验证逻辑
  if (!maxCount || (maxCount < 1)) {
    Toast(`最多上传${maxCount}张`)
    return []
  }

  const res = await getFiles(option)

  if (res.result !== 0) {
    const errMsg = '上传出错，请稍后重试'
    Toast(errMsg)
    return Promise.reject(errMsg)
  }

  // 提示文件大小校验
  let files = res.value.filter(item => item.size <= maxSize)
  if (res.value.length !== files.length) {
    Toast(`图片大小不能超过${parseFloat(`${(maxSize / 1024 / 1024).toFixed(2)}`)}M`)
  }

  // 验证文件尺寸
  if (cropRatio && mode !== 'tackPicture') {
    const originCount = files.length
    files = files.filter(item => ((item.height || 0) / (item.width || 0)).toFixed(3) === cropRatio.toFixed(3))
    if (originCount !== files.length) {
      Toast('图片长宽比例不匹配')
    }
  }

  const ret:UploadItemRet[] = []
  // 开始上传，同时保证上传顺序
  await Promise.all(files.map(item => {
    const retItem:UploadItemRet = {
      id: uniqueId('upload'),
      height: item.height,
      width: item.width,
      size: item.size,
      extension: 'png',
      uploadPercent: 0,
      path: '',
      link: '',
    }
    ret.push(retItem)
    onProgress?.(retItem)
    return uploadItem(item, retItem, option)
  }))
  return ret
}
