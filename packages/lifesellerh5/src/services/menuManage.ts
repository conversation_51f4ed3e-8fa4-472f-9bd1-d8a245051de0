// import { httpRequest } from '@/utils/http'
import {
  IData,
  IDishList,
  PriceType,
  SpecialtyType,
  ResourceType
} from '~/types/menu'
import {
  postUpsertMenuDish
} from './edith_post_upsert_menu_dish'

// Mock 数据
export const mockMenuData: IData = {
  menuGroupList: [
    {
      groupId: 'group-1',
      groupName: '主食类',
      sortOrder: 1,
      dishList: [
        {
          dishId: 'dish-1',
          dishName: '松叶蟹腿锅物',
          priceItem: {
            dishPrice: 298,
            priceType: PriceType.FIXED
          },
          dishResourceList: [
            {
              resourceId: 'dish-1-img',
              type: ResourceType.IMAGE,
              resourceInfo: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=300&h=300&fit=crop'
            }
          ],
          specialty: SpecialtyType.YES,
          recommendSortOrder: 0,
          sortOrder: 0
        },
        {
          dishId: 'dish-3',
          dishName: '蒜蓉粉丝扇贝',
          priceItem: {
            dishPrice: 68,
            priceType: PriceType.FIXED
          },
          dishResourceList: [
            {
              resourceId: 'dish-3-img',
              type: ResourceType.IMAGE,
              resourceInfo: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=300&h=300&fit=crop'
            }
          ],
          specialty: SpecialtyType.NO,
          sortOrder: 1
        },
        {
          dishId: 'dish-4',
          dishName: '清蒸石斑鱼',
          priceItem: {
            dishPrice: 128,
            priceType: PriceType.STARTING
          },
          dishResourceList: [
            {
              resourceId: 'dish-4-img',
              type: ResourceType.IMAGE,
              resourceInfo: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=300&h=300&fit=crop'
            }
          ],
          specialty: SpecialtyType.NO,
          sortOrder: 2
        },
        {
          dishId: 'dish-5',
          dishName: '红烧肉',
          priceItem: {
            dishPrice: 58,
            priceType: PriceType.FIXED
          },
          dishResourceList: [
            {
              resourceId: 'dish-5-img',
              type: ResourceType.IMAGE,
              resourceInfo: 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=300&h=300&fit=crop'
            }
          ],
          specialty: SpecialtyType.NO,
          sortOrder: 3
        }
      ]
    },
    {
      groupId: 'group-2',
      groupName: '汤品类',
      sortOrder: 2,
      dishList: [
        {
          dishId: 'dish-2',
          dishName: '炭烤鸽腿',
          priceItem: {
            dishPrice: 88,
            priceType: PriceType.STARTING
          },
          dishResourceList: [
            {
              resourceId: 'dish-2-img',
              type: ResourceType.IMAGE,
              resourceInfo: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=300&h=300&fit=crop'
            }
          ],
          specialty: SpecialtyType.YES,
          recommendSortOrder: 1,
          sortOrder: 0
        },
        {
          dishId: 'dish-6',
          dishName: '老火靓汤',
          priceItem: {
            dishPrice: 38,
            priceType: PriceType.FIXED
          },
          dishResourceList: [
            {
              resourceId: 'dish-6-img',
              type: ResourceType.IMAGE,
              resourceInfo: 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=300&h=300&fit=crop'
            }
          ],
          specialty: SpecialtyType.NO,
          sortOrder: 1
        },
        {
          dishId: 'dish-7',
          dishName: '番茄鸡蛋汤',
          priceItem: {
            dishPrice: 28,
            priceType: PriceType.FIXED
          },
          dishResourceList: [
            {
              resourceId: 'dish-7-img',
              type: ResourceType.IMAGE,
              resourceInfo: 'https://images.unsplash.com/photo-1513639776629-7b61b0ac49cb?w=300&h=300&fit=crop'
            }
          ],
          specialty: SpecialtyType.NO,
          sortOrder: 2
        }
      ]
    }
  ]
}

// 获取菜单数据
export const getMenuData = async (poiId: string): Promise<IData> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 500))

  console.log('Mock: 获取菜单数据 poiId:', poiId)
  return mockMenuData

  // 正式环境使用下面的代码
  // const response = await httpRequest({
  //   url: '/api/menu/getMenuData',
  //   method: 'POST',
  //   data: {
  //     poiId
  //   }
  // })
  // return response.data
}

// 保存菜单数据
export const saveMenuData = async (poiId: string, menuData: IData): Promise<void> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 800))

  console.log('Mock: 保存菜单数据 poiId:', poiId, 'menuData:', menuData)

  // 更新 mock 数据
  mockMenuData.menuGroupList = menuData.menuGroupList

  // 正式环境使用下面的代码
  // const response = await httpRequest({
  //   url: '/api/menu/saveMenuData',
  //   method: 'POST',
  //   data: {
  //     poiId,
  //     ...menuData
  //   }
  // })
}

// 获取菜品详情
export const getDishDetail = async (dishId: string): Promise<IDishList> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 300))

  console.log('Mock: 获取菜品详情 dishId:', dishId)

  // 从 mock 数据中查找菜品
  for (const group of mockMenuData.menuGroupList) {
    const dish = group.dishList.find(d => d.dishId === dishId)
    if (dish) {
      return dish
    }
  }

  throw new Error('菜品不存在')

  // 正式环境使用下面的代码
  // const response = await httpRequest({
  //   url: '/api/menu/getDishDetail',
  //   method: 'POST',
  //   data: {
  //     dishId
  //   }
  // })
  // return response.data
}

// 类型转换函数：将内部类型转换为API类型
const convertDishToApi = (dish: IDishList): any => ({
  dishId: dish.dishId,
  dishName: dish.dishName,
  priceItem: dish.priceItem,
  dishResourceList: dish.dishResourceList.map(resource => ({
    resourceId: resource.resourceId,
    type: resource.type,
    resourceInfo: resource.resourceInfo,
    mediaId: resource.mediaId,
    sortOrder: resource.sortOrder || 0
  })),
  dishStatus: dish.dishStatus,
  specialty: dish.specialty,
  sortOrder: dish.sortOrder,
  dishSource: dish.dishSource || 0,
  recommendSortOrder: dish.recommendSortOrder
})

const convertMenuGroupToApi = (group: any): any => ({
  groupId: group.groupId,
  groupName: group.groupName,
  sortOrder: group.sortOrder,
  groupStatus: group.groupStatus,
  dishList: group.dishList.map(convertDishToApi)
})

// 保存菜品
export const saveDish = async (
  dishData: Partial<IDishList>,
  groupId?: string,
  poiId?: string,
  userId?: string
): Promise<void> => {
  try {
    // 获取实际的用户ID和POI ID
    const actualUserId = userId || 'current_user_id' // 这里需要从实际的用户状态获取
    const actualPoiId = poiId || 'current_poi_id' // 这里需要从实际的POI状态获取
    const actualGroupId = groupId || (dishData as any).groupId || 'default_group'

    // 构建符合接口要求的数据结构
    const dishForApi = {
      dishId: dishData.dishId || `dish-${Date.now()}`,
      dishName: dishData.dishName!,
      priceItem: dishData.priceItem!,
      dishResourceList: dishData.dishResourceList || [],
      specialty: dishData.specialty!,
      sortOrder: dishData.sortOrder!,
      dishSource: dishData.dishSource || 0,
      recommendSortOrder: dishData.recommendSortOrder
    }

    // 如果是编辑现有菜品，需要获取完整的菜单数据进行更新
    if (dishData.dishId) {
      // 编辑模式：获取当前菜单数据，更新指定菜品
      const currentMenuData = await getMenuData(actualPoiId)
      const updatedMenuGroupList = currentMenuData.menuGroupList.map(group => {
        const dishIndex = group.dishList.findIndex(d => d.dishId === dishData.dishId)
        if (dishIndex > -1) {
          // 更新菜品
          group.dishList[dishIndex] = { ...group.dishList[dishIndex], ...dishForApi } as IDishList
        }
        return group
      })

      const payload: any = {
        userId: actualUserId,
        poiId: actualPoiId,
        menuGroupList: updatedMenuGroupList.map(convertMenuGroupToApi)
      }

      await postUpsertMenuDish(payload)
    } else {
      // 新增模式：添加到指定分组
      const currentMenuData = await getMenuData(actualPoiId)
      const newDish: IDishList = {
        ...dishForApi,
        dishId: dishForApi.dishId,
        dishSource: dishForApi.dishSource
      }

      const updatedMenuGroupList = currentMenuData.menuGroupList.map(group => {
        if (group.groupId === actualGroupId) {
          return {
            ...group,
            dishList: [...group.dishList, newDish]
          }
        }
        return group
      })

      const payload: any = {
        userId: actualUserId,
        poiId: actualPoiId,
        menuGroupList: updatedMenuGroupList.map(convertMenuGroupToApi)
      }

      await postUpsertMenuDish(payload)
    }

    console.log('真实接口: 保存菜品成功', dishData)
  } catch (error) {
    console.error('真实接口调用失败，使用mock数据:', error)

    // 兜底：使用mock逻辑
    await new Promise(resolve => setTimeout(resolve, 600))

    console.log('Mock: 保存菜品', dishData)

    if (dishData.dishId) {
      // 编辑菜品
      for (const group of mockMenuData.menuGroupList) {
        const dishIndex = group.dishList.findIndex(d => d.dishId === dishData.dishId)
        if (dishIndex > -1) {
          group.dishList[dishIndex] = { ...group.dishList[dishIndex], ...dishData } as IDishList
          break
        }
      }
    } else {
      // 新增菜品
      const newDish: IDishList = {
        dishId: `dish-${Date.now()}`,
        dishName: dishData.dishName || '',
        priceItem: dishData.priceItem || { dishPrice: 0, priceType: PriceType.FIXED },
        dishResourceList: dishData.dishResourceList || [],
        specialty: dishData.specialty || SpecialtyType.NO,
        sortOrder: dishData.sortOrder || 0,
        dishSource: dishData.dishSource || 0,
        recommendSortOrder: dishData.recommendSortOrder
      }

      // 找到对应的分组
      const targetGroupId = groupId || (dishData as any).groupId
      if (targetGroupId) {
        const group = mockMenuData.menuGroupList.find(g => g.groupId === targetGroupId)
        if (group) {
          group.dishList.push(newDish)
        }
      }
    }
  }
}

// 验证分组名称
export const validateGroupName = async (name: string): Promise<boolean> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 200))

  console.log('Mock: 验证分组名称', name)

  // 敏感词检查
  const sensitiveWords = ['黄赌毒', '违法', '敏感词']
  const hasSensitiveWord = sensitiveWords.some(word => name.includes(word))

  if (hasSensitiveWord) {
    return false
  }

  return true

  // 正式环境使用下面的代码
  // const response = await httpRequest({
  //   url: '/api/menu/validateGroupName',
  //   method: 'POST',
  //   data: { name }
  // })
  // return response.data.isValid
}

// 验证菜品信息
export const validateDish = async (dishData: { name: string; image: string }): Promise<{
  isValid: boolean
  failedField?: string
}> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 300))

  console.log('Mock: 验证菜品信息', dishData)

  // 敏感词检查
  const sensitiveWords = ['黄赌毒', '违法', '敏感词']
  const hasSensitiveWordInName = sensitiveWords.some(word => dishData.name.includes(word))

  if (hasSensitiveWordInName) {
    return {
      isValid: false,
      failedField: '名称'
    }
  }

  // 图片检查 (模拟检查)
  if (dishData.image.includes('invalid')) {
    return {
      isValid: false,
      failedField: '图片'
    }
  }

  return {
    isValid: true
  }

  // 正式环境使用下面的代码
  // const response = await httpRequest({
  //   url: '/api/menu/validateDish',
  //   method: 'POST',
  //   data: dishData
  // })
  // return response.data
}

// 菜品去重检查
export const checkDishDuplicate = async (params: {
  name: string
  price: number
  excludeId?: string
}): Promise<boolean> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 200))

  console.log('Mock: 菜品去重检查', params)

  // 在 mock 数据中查找重复的菜品
  for (const group of mockMenuData.menuGroupList) {
    const duplicateDish = group.dishList.find(dish => dish.dishName === params.name && dish.priceItem.dishPrice === params.price && dish.dishId !== params.excludeId)
    if (duplicateDish) {
      return true
    }
  }

  return false

  // 正式环境使用下面的代码
  // const response = await httpRequest({
  //   url: '/api/menu/checkDishDuplicate',
  //   method: 'POST',
  //   data: params
  // })
  // return response.data.isDuplicate
}

// 上传菜品图片
export const uploadDishImage = async (file: File): Promise<string> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 1000))

  console.log('Mock: 上传菜品图片', file)

  // 模拟返回图片URL
  const imageUrl = `https://images.unsplash.com/photo-${Date.now()}?w=400&h=400&fit=crop`
  return imageUrl

  // 正式环境使用下面的代码
  // const formData = new FormData()
  // formData.append('file', file)
  // const response = await httpRequest({
  //   url: '/api/menu/uploadDishImage',
  //   method: 'POST',
  //   data: formData
  // })
  // return response.data.imageUrl
}

// 获取招牌菜数量
export const getSpecialtyDishCount = async (poiId: string): Promise<number> => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 200))

  console.log('Mock: 获取招牌菜数量 poiId:', poiId)

  // 统计招牌菜数量
  let count = 0
  for (const group of mockMenuData.menuGroupList) {
    count += group.dishList.filter(dish => dish.specialty === SpecialtyType.YES).length
  }

  return count

  // 正式环境使用下面的代码
  // const response = await httpRequest({
  //   url: '/api/menu/getSpecialtyDishCount',
  //   method: 'POST',
  //   data: { poiId }
  // })
  // return response.data.count
}
